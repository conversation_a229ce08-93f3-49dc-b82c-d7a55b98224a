# Ancestry and Gender Bias Analysis
# Analysis of psychAD media dataset for equity and representation bias

# Load dependencies
source("global_aes_out.R")
source("stat_functions.R")

# Additional libraries for this analysis
library(dplyr)
library(tidyr)
library(stringr)
# Skip kableExtra for now to avoid dependency issues

# Set global theme for larger fonts
theme_set(theme_minimal(base_size = 14))  # Increase base font size

# Initialize logging
log_file <- "out/psychAD_analysis_log.txt"
cat("", file = log_file)  # Clear the log file

# Function to log messages to both console and file
log_message <- function(message) {
  cat(message, "\n")
  cat(message, "\n", file = log_file, append = TRUE)
}

# Define consistent color palettes for ancestry and sex (used throughout all plots)
ancestry_colors <- c("AFR" = "#E31A1C", "AMR" = "#FF7F00", "EAS" = "#1F78B4", 
                     "EUR" = "#6A3D9A", "SAS" = "#FB9A99", 
                     "Unknown" = "#CAB2D6")

sex_colors <- c("female" = "#E69F00", "male" = "#56B4E9")

# Load data
cat("Loading psychAD media dataset...\n")
data <- read.csv("data/psych-AD_media-1.csv", stringsAsFactors = FALSE)

# Combine EAS_SAS into Unknown/Other category
data$Ancestry_clean <- ifelse(data$Ancestry == "EAS_SAS", "Unknown", data$Ancestry)

# Basic data overview
cat("Dataset loaded successfully!\n")
cat("Total samples:", nrow(data), "\n")
cat("Total variables:", ncol(data), "\n")

# ============================================================================
# 1. DATASET COMPOSITION OVERVIEW
# ============================================================================

cat("\n=== DATASET COMPOSITION OVERVIEW ===\n")

# Basic counts
total_samples <- nrow(data)
cohort_counts <- table(data$cohort)
cat("Total samples:", total_samples, "\n")
cat("Cohort distribution:\n")
print(cohort_counts)

# ============================================================================
# 2. ANCESTRY DISTRIBUTION ANALYSIS  
# ============================================================================

cat("\n=== ANCESTRY DISTRIBUTION ANALYSIS ===\n")

# Clean and examine ancestry data
ancestry_counts <- table(data$Ancestry_clean, useNA = "always")
cat("Ancestry distribution:\n")
print(ancestry_counts)

# Calculate percentages
ancestry_pct <- round(prop.table(ancestry_counts) * 100, 2)
cat("\nAncestry percentages:\n")
print(ancestry_pct)

# Create ancestry summary table
ancestry_summary <- data.frame(
  Ancestry = names(ancestry_counts),
  Count = as.numeric(ancestry_counts),
  Percentage = as.numeric(ancestry_pct)
)

# Visualize ancestry distribution
p1 <- ggplot(data = subset(ancestry_summary, !is.na(Ancestry)), 
             aes(x = reorder(Ancestry, -Count), y = Count, fill = Ancestry)) +
  geom_bar(stat = "identity") +
  theme_minimal(base_size = 14) +
  labs(title = "Sample Distribution by Ancestry",
       x = "Ancestry Group", 
       y = "Number of Samples") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1, size = 12),
        axis.text.y = element_text(size = 12),
        axis.title = element_text(size = 14),
        title = element_text(size = 16),
        legend.position = "none") +
  scale_fill_manual(values = ancestry_colors)

ggsave("out/ancestry_distribution_barplot.pdf", p1, width = 4, height = 3)
cat("Saved: ancestry_distribution_barplot.pdf\n")

# Pie chart for ancestry
p2 <- ggplot(data = subset(ancestry_summary, !is.na(Ancestry)), 
             aes(x = "", y = Count, fill = Ancestry)) +
  geom_bar(stat = "identity", width = 1) +
  coord_polar("y", start = 0) +
  theme_void(base_size = 14) +
  labs(title = "Ancestry Distribution (Pie Chart)") +
  theme(title = element_text(size = 16),
        legend.text = element_text(size = 12)) +
  scale_fill_manual(values = ancestry_colors)

ggsave("out/ancestry_distribution_pie.pdf", p2, width = 4, height = 3)
cat("Saved: ancestry_distribution_pie.pdf\n")

# ============================================================================
# 3. GENDER DISTRIBUTION ANALYSIS
# ============================================================================

cat("\n=== GENDER DISTRIBUTION ANALYSIS ===\n")

# Overall gender distribution
sex_counts <- table(data$sex, useNA = "always")
cat("Overall gender distribution:\n")
print(sex_counts)

sex_pct <- round(prop.table(sex_counts) * 100, 2)
cat("\nGender percentages:\n")
print(sex_pct)

# Gender distribution by ancestry
sex_ancestry_table <- table(data$Ancestry_clean, data$sex, useNA = "always")
cat("\nGender by ancestry cross-tabulation:\n")
print(sex_ancestry_table)

# Convert to proportions within each ancestry
sex_ancestry_prop <- prop.table(sex_ancestry_table, margin = 1)
cat("\nGender proportions within each ancestry:\n")
print(round(sex_ancestry_prop, 3))

# Visualize gender by ancestry
# Prepare data for plotting
plot_data <- data %>%
  filter(!is.na(Ancestry_clean) & !is.na(sex)) %>%
  count(Ancestry_clean, sex) %>%
  group_by(Ancestry_clean) %>%
  mutate(prop = n/sum(n))

p3 <- ggplot(plot_data, aes(x = Ancestry_clean, y = n, fill = sex)) +
  geom_bar(stat = "identity", position = "dodge") +
  theme_minimal(base_size = 14) +
  labs(title = "Gender Distribution by Ancestry",
       x = "Ancestry Group",
       y = "Number of Samples",
       fill = "Sex") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1, size = 12),
        axis.text.y = element_text(size = 12),
        axis.title = element_text(size = 14),
        title = element_text(size = 16),
        legend.text = element_text(size = 12),
        legend.title = element_text(size = 14)) +
  scale_fill_manual(values = sex_colors)

ggsave("out/gender_by_ancestry_barplot.pdf", p3, width = 5, height = 3)
cat("Saved: gender_by_ancestry_barplot.pdf\n")

# Stacked proportional bar chart
p4 <- ggplot(plot_data, aes(x = Ancestry_clean, y = prop, fill = sex)) +
  geom_bar(stat = "identity", position = "stack") +
  theme_minimal(base_size = 14) +
  labs(title = "Gender Proportions by Ancestry",
       x = "Ancestry Group",
       y = "Proportion",
       fill = "Sex") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1, size = 12),
        axis.text.y = element_text(size = 12),
        axis.title = element_text(size = 14),
        title = element_text(size = 16),
        legend.text = element_text(size = 12),
        legend.title = element_text(size = 14)) +
  scale_fill_manual(values = sex_colors) +
  scale_y_continuous(labels = scales::percent)

ggsave("out/gender_proportions_by_ancestry.pdf", p4, width = 5, height = 3)
cat("Saved: gender_proportions_by_ancestry.pdf\n")

cat("\n=== STEP 1-3 COMPLETED ===\n")
cat("✓ Dataset composition overview completed\n")
cat("✓ Ancestry distribution analysis completed\n") 
cat("✓ Gender distribution analysis completed\n")

# ============================================================================
# 4. REPRESENTATION BIAS ANALYSIS
# ============================================================================

cat("\n=== REPRESENTATION BIAS ANALYSIS ===\n")

# Intersectional analysis (ancestry × gender)
intersectional_counts <- data %>%
  filter(!is.na(Ancestry_clean) & !is.na(sex)) %>%
  count(Ancestry_clean, sex) %>%
  spread(sex, n, fill = 0) %>%
  mutate(Total = female + male,
         Female_Pct = round(female/Total * 100, 1),
         Male_Pct = round(male/Total * 100, 1))

cat("Intersectional analysis (Ancestry × Gender):\n")
print(intersectional_counts)

# Identify underrepresented groups
total_samples_clean <- sum(intersectional_counts$Total)
intersectional_counts$Overall_Pct <- round(intersectional_counts$Total / total_samples_clean * 100, 2)

# Flag groups with < 5% representation
underrep_threshold <- 5
underrepresented <- intersectional_counts[intersectional_counts$Overall_Pct < underrep_threshold, ]
cat("\nUnderrepresented ancestry groups (< 5% of total):\n")
print(underrepresented)

# Create heatmap showing sample counts
heatmap_data <- data %>%
  filter(!is.na(Ancestry_clean) & !is.na(sex)) %>%
  count(Ancestry_clean, sex)

p5 <- ggplot(heatmap_data, aes(x = sex, y = Ancestry_clean, fill = n)) +
  geom_tile(color = "white") +
  geom_text(aes(label = n), color = "white", size = 5) +
  scale_fill_gradient(low = "lightblue", high = "darkblue", name = "Count") +
  theme_minimal(base_size = 14) +
  labs(title = "Sample Count Heatmap: Ancestry × Gender",
       x = "Gender", y = "Ancestry") +
  theme(axis.text.x = element_text(angle = 0, size = 12),
        axis.text.y = element_text(size = 12),
        axis.title = element_text(size = 14),
        title = element_text(size = 16),
        legend.text = element_text(size = 12),
        legend.title = element_text(size = 14))

ggsave("out/intersectional_heatmap.pdf", p5, width = 4, height = 3)
cat("Saved: intersectional_heatmap.pdf\n")

# Missing data patterns by demographics
missing_analysis <- data %>%
  select(Ancestry_clean, sex, ageDeath, PMI, apoeGenotype, CERAD, Braak) %>%
  gather(variable, value, -Ancestry_clean, -sex) %>%
  group_by(Ancestry_clean, sex, variable) %>%
  summarise(missing_count = sum(is.na(value)),
            total_count = n(),
            missing_pct = round(missing_count/total_count * 100, 1),
            .groups = 'drop')

cat("\nMissing data patterns by ancestry and gender:\n")
print(missing_analysis)

cat("\n=== STEP 4 COMPLETED ===\n")
cat("✓ Representation bias analysis completed\n")

# ============================================================================
# ADDITIONAL PLOTS REQUESTED
# ============================================================================

cat("\n=== STATISTICAL ANALYSIS ===\n")

# Load additional packages for statistical tests
if (!require(car, quietly = TRUE)) {
  install.packages("car")
  library(car)
}

# 1. Hypothesis Testing
cat("\n--- Hypothesis Testing ---\n")

# Chi-square test for ancestry distribution vs expected equal distribution
ancestry_for_test <- data$Ancestry_clean[!is.na(data$Ancestry_clean) & data$Ancestry_clean != "Unknown"]
chisq_ancestry <- chisq.test(table(ancestry_for_test))
cat("Chi-square test for ancestry distribution:\n")
cat("X-squared =", chisq_ancestry$statistic, ", p-value =", chisq_ancestry$p.value, "\n")

# Chi-square test for gender balance within each ancestry
for (anc in unique(ancestry_for_test)) {
  anc_data <- data[data$Ancestry_clean == anc & !is.na(data$sex), ]
  if (nrow(anc_data) > 10) {  # Only test if sufficient sample size
    gender_test <- chisq.test(table(anc_data$sex))
    cat("Gender balance in", anc, "- Chi-square p-value:", gender_test$p.value, "\n")
  }
}

# 2. Equity Metrics
cat("\n--- Equity Metrics ---\n")

# Calculate representation ratios (actual vs expected equal representation)
total_samples_analysis <- nrow(data[!is.na(data$Ancestry_clean) & data$Ancestry_clean != "Unknown", ])
unique_ancestries <- unique(data$Ancestry_clean[!is.na(data$Ancestry_clean) & data$Ancestry_clean != "Unknown"])
expected_equal <- total_samples_analysis / length(unique_ancestries)

equity_metrics <- data %>%
  filter(!is.na(Ancestry_clean) & Ancestry_clean != "Unknown") %>%
  count(Ancestry_clean) %>%
  mutate(
    expected_equal = expected_equal,
    representation_ratio = n / expected_equal,
    equity_index = ifelse(representation_ratio > 1, 1/representation_ratio, representation_ratio)
  )

cat("Representation ratios (>1 = overrepresented, <1 = underrepresented):\n")
print(equity_metrics)

# Simpson's Diversity Index
simpson_diversity <- 1 - sum((equity_metrics$n / sum(equity_metrics$n))^2)
cat("Simpson's Diversity Index:", round(simpson_diversity, 3), "\n")

# Shannon's Diversity Index
shannon_diversity <- -sum((equity_metrics$n / sum(equity_metrics$n)) * log(equity_metrics$n / sum(equity_metrics$n)))
cat("Shannon's Diversity Index:", round(shannon_diversity, 3), "\n")

# 3. Missing Data Analysis
cat("\n--- Missing Data Analysis ---\n")

# Calculate missing data percentages by ancestry and variable
missing_summary <- data %>%
  select(Ancestry_clean, PMI, apoeGenotype, CERAD, Braak, Dementia) %>%
  filter(!is.na(Ancestry_clean)) %>%
  group_by(Ancestry_clean) %>%
  summarise(
    n_total = n(),
    PMI_missing = sum(is.na(PMI)) / n() * 100,
    apoeGenotype_missing = sum(is.na(apoeGenotype)) / n() * 100,
    CERAD_missing = sum(is.na(CERAD)) / n() * 100,
    Braak_missing = sum(is.na(Braak)) / n() * 100,
    Dementia_missing = sum(is.na(Dementia)) / n() * 100,
    .groups = 'drop'
  )

cat("Missing data percentages by ancestry:\n")
missing_summary_numeric <- missing_summary[, -1]  # Remove Ancestry column for rounding
missing_summary_rounded <- cbind(missing_summary[1], round(missing_summary_numeric, 1))
print(missing_summary_rounded)

# Create summary report
cat("\n=== ANALYSIS SUMMARY REPORT ===\n")
cat("✓ Dataset contains", nrow(data), "samples across", length(unique(data$cohort)), "cohorts\n")
cat("✓ Ancestry distribution: EUR dominant, AFR substantial, others underrepresented\n")
cat("✓ Gender balance: Overall balanced, but varies by ancestry\n")
cat("✓ Representation bias: EAS, SAS, Unknown groups severely underrepresented\n")
cat("✓ Missing data: Substantial gaps in clinical measures (CERAD, Braak) for some ancestry groups\n")
cat("✓ Power concerns: Small ancestry groups may be underpowered for analyses\n")

cat("\n=== ALL ANALYSIS STEPS COMPLETED ===\n")
cat("✓ Dataset composition overview completed\n")
cat("✓ Ancestry distribution analysis completed\n") 
cat("✓ Gender distribution analysis completed\n")
cat("✓ Representation bias analysis completed\n")
cat("✓ Statistical analysis completed\n")
cat("✓ Equity metrics calculated\n")
cat("✓ Additional plots completed\n")

# Save the workspace for further analysis
save.image("out/equity_bias_analysis_workspace.RData")
cat("✓ Workspace saved to: equity_bias_analysis_workspace.RData\n")

# ============================================================================
# ADDITIONAL PLOTS REQUESTED
# ============================================================================

cat("\n=== ADDITIONAL VISUALIZATION ANALYSIS ===\n")

# 1. Ancestry distribution of the whole cohort (bar plot)
ancestry_dist_data <- data %>%
  filter(!is.na(Ancestry_clean)) %>%
  count(Ancestry_clean) %>%
  mutate(percentage = round(n/sum(n)*100, 1))

p_ancestry_overall <- ggplot(ancestry_dist_data, aes(x = reorder(Ancestry_clean, -n), y = n, fill = Ancestry_clean)) +
  geom_bar(stat = "identity") +
  geom_text(aes(label = paste0(n, "\n(", percentage, "%)")), 
            vjust = -0.5, size = 3) +
  theme_minimal(base_size = 14) +
  labs(title = "Ancestry Distribution - Whole Cohort",
       x = "Ancestry Group", 
       y = "Number of Samples") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1, size = 12),
        axis.text.y = element_text(size = 12),
        axis.title = element_text(size = 14),
        title = element_text(size = 16),
        legend.position = "none") +
  scale_fill_manual(values = ancestry_colors)

ggsave("out/ancestry_distribution_whole_cohort.pdf", p_ancestry_overall, width = 4, height = 3)
cat("Saved: ancestry_distribution_whole_cohort.pdf\n")

# 2. 100% stacked bar chart colored by ancestry for each disease diagnosis
# Combine cross-disorder and single diagnosis into unified disease categories
# Create disease analysis data with combined diagnoses
disease_ancestry_data <- data %>%
  filter(!is.na(Ancestry_clean)) %>%
  mutate(
    # Combine AD cross-disorder and AD diagnosis
    AD_combined = ifelse((crossDis_AD == 1 | dx_AD == 1) %in% TRUE, 1, 0),
    # Use individual cross-disorders for others
    SCZ = ifelse(crossDis_SCZ == 1 %in% TRUE, 1, 0),
    DLBD = ifelse(crossDis_DLBD == 1 %in% TRUE, 1, 0),
    Vascular = ifelse(crossDis_Vas == 1 %in% TRUE, 1, 0),
    BD = ifelse(crossDis_BD == 1 %in% TRUE, 1, 0),
    Tau = ifelse(crossDis_Tau == 1 %in% TRUE, 1, 0),
    PD = ifelse(crossDis_PD == 1 %in% TRUE, 1, 0),
    FTD = ifelse(crossDis_FTD == 1 %in% TRUE, 1, 0),
    Dementia = ifelse(Dementia == 1 %in% TRUE, 1, 0)
  ) %>%
  select(Ancestry_clean, AD_combined, SCZ, DLBD, Vascular, BD, Tau, PD, FTD, Dementia) %>%
  gather(disease, status, -Ancestry_clean) %>%
  filter(!is.na(status) & status == 1) %>%  # Only include positive diagnoses
  count(disease, Ancestry_clean) %>%
  group_by(disease) %>%
  mutate(total = sum(n),
         prop = n/total) %>%
  ungroup() %>%
  mutate(disease_label = case_when(
    disease == 'AD_combined' ~ paste0('AD (n=', total, ')'),
    disease == 'SCZ' ~ paste0('SCZ (n=', total, ')'),
    disease == 'DLBD' ~ paste0('DLBD (n=', total, ')'),
    disease == 'Vascular' ~ paste0('Vascular (n=', total, ')'),
    disease == 'BD' ~ paste0('BD (n=', total, ')'),
    disease == 'Tau' ~ paste0('Tau (n=', total, ')'),
    disease == 'PD' ~ paste0('PD (n=', total, ')'),
    disease == 'FTD' ~ paste0('FTD (n=', total, ')'),
    disease == 'Dementia' ~ paste0('Dementia (n=', total, ')')
  ))

p_disease_ancestry <- ggplot(disease_ancestry_data, aes(x = disease_label, y = prop, fill = Ancestry_clean)) +
  geom_bar(stat = "identity", position = "stack") +
  theme_minimal(base_size = 14) +
  labs(title = "Disease Diagnoses by Ancestry (100% Stacked)",
       x = "Disease Diagnosis", 
       y = "Proportion",
       fill = "Ancestry") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
        axis.text.y = element_text(size = 12),
        axis.title = element_text(size = 14),
        title = element_text(size = 16),
        legend.text = element_text(size = 12),
        legend.title = element_text(size = 14)) +
  scale_fill_manual(values = ancestry_colors) +
  scale_y_continuous(labels = scales::percent)

ggsave("out/disease_diagnoses_by_ancestry_stacked.pdf", p_disease_ancestry, width = 6, height = 4)
cat("Saved: disease_diagnoses_by_ancestry_stacked.pdf\n")

# 3. 100% stacked bar chart colored by sex for each disease diagnosis
disease_sex_data <- data %>%
  filter(!is.na(sex)) %>%
  mutate(
    # Combine AD cross-disorder and AD diagnosis
    AD_combined = ifelse((crossDis_AD == 1 | dx_AD == 1) %in% TRUE, 1, 0),
    # Use individual cross-disorders for others
    SCZ = ifelse(crossDis_SCZ == 1 %in% TRUE, 1, 0),
    DLBD = ifelse(crossDis_DLBD == 1 %in% TRUE, 1, 0),
    Vascular = ifelse(crossDis_Vas == 1 %in% TRUE, 1, 0),
    BD = ifelse(crossDis_BD == 1 %in% TRUE, 1, 0),
    Tau = ifelse(crossDis_Tau == 1 %in% TRUE, 1, 0),
    PD = ifelse(crossDis_PD == 1 %in% TRUE, 1, 0),
    FTD = ifelse(crossDis_FTD == 1 %in% TRUE, 1, 0),
    Dementia = ifelse(Dementia == 1 %in% TRUE, 1, 0)
  ) %>%
  select(sex, AD_combined, SCZ, DLBD, Vascular, BD, Tau, PD, FTD, Dementia) %>%
  gather(disease, status, -sex) %>%
  filter(!is.na(status) & status == 1) %>%  # Only include positive diagnoses
  count(disease, sex) %>%
  group_by(disease) %>%
  mutate(total = sum(n),
         prop = n/total) %>%
  ungroup() %>%
  mutate(disease_label = case_when(
    disease == 'AD_combined' ~ paste0('AD (n=', total, ')'),
    disease == 'SCZ' ~ paste0('SCZ (n=', total, ')'),
    disease == 'DLBD' ~ paste0('DLBD (n=', total, ')'),
    disease == 'Vascular' ~ paste0('Vascular (n=', total, ')'),
    disease == 'BD' ~ paste0('BD (n=', total, ')'),
    disease == 'Tau' ~ paste0('Tau (n=', total, ')'),
    disease == 'PD' ~ paste0('PD (n=', total, ')'),
    disease == 'FTD' ~ paste0('FTD (n=', total, ')'),
    disease == 'Dementia' ~ paste0('Dementia (n=', total, ')')
  ))

p_disease_sex <- ggplot(disease_sex_data, aes(x = disease_label, y = prop, fill = sex)) +
  geom_bar(stat = "identity", position = "stack") +
  theme_minimal(base_size = 14) +
  labs(title = "Disease Diagnoses by Sex (100% Stacked)",
       x = "Disease Diagnosis", 
       y = "Proportion",
       fill = "Sex") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
        axis.text.y = element_text(size = 12),
        axis.title = element_text(size = 14),
        title = element_text(size = 16),
        legend.text = element_text(size = 12),
        legend.title = element_text(size = 14)) +
  scale_fill_manual(values = sex_colors) +
  scale_y_continuous(labels = scales::percent)

ggsave("out/disease_diagnoses_by_sex_stacked.pdf", p_disease_sex, width = 6, height = 4)
cat("Saved: disease_diagnoses_by_sex_stacked.pdf\n")

cat("\n=== ADDITIONAL PLOTS COMPLETED ===\n")
cat("✓ Ancestry distribution whole cohort completed\n")
cat("✓ Disease diagnoses by ancestry (stacked) completed\n") 
cat("✓ Disease diagnoses by sex (stacked) completed\n")
