# Ancestry and Gender Bias Analysis - HCA Dataset
# Analysis of HCA dataset for equity and representation bias by tissue type

# Load dependencies
source("global_aes_out.R")
source("stat_functions.R")

# Additional libraries for this analysis
library(dplyr)
library(tidyr)
library(stringr)
library(readxl)

# Set global theme for larger fonts
theme_set(theme_minimal(base_size = 14))

# Define consistent color palettes for ancestry and sex (SAME AS PSYCHAD)
ancestry_colors <- c("AFR" = "#E31A1C", "AMR" = "#FF7F00", "EAS" = "#1F78B4", 
                     "EUR" = "#6A3D9A", "SAS" = "#FB9A99", 
                     "Unknown" = "#CAB2D6")

sex_colors <- c("female" = "#E69F00", "male" = "#56B4E9")

# Load HCA data
cat("Loading HCA dataset...\n")
data_raw <- read_excel("data/HCA scrnaseq data final.xlsx")

# Clean and standardize the data
cat("Cleaning and standardizing HCA data...\n")

# Get the actual data starting from row 4 (skip headers)
data_clean <- data_raw[5:nrow(data_raw), ]  # Skip first 4 rows
colnames(data_clean) <- as.character(data_raw[3, ])  # Use row 3 as column names

# Fix any NA or empty column names and make them unique
col_names <- colnames(data_clean)
col_names[is.na(col_names) | col_names == ""] <- paste0("Col_", seq_len(sum(is.na(col_names) | col_names == "")))
colnames(data_clean) <- make.names(col_names, unique = TRUE)

# Standardize race/ancestry categories to match psychAD
data <- data_clean %>%
  mutate(
    # Clean ethnicity column and map to standard categories
    Race_clean = case_when(
      tolower(`donor_organism.human_specific.ethnicity.text`) %in% c("white") ~ "EUR",
      tolower(`donor_organism.human_specific.ethnicity.text`) %in% c("black") ~ "AFR", 
      tolower(`donor_organism.human_specific.ethnicity.text`) %in% c("asian") ~ "EAS",
      tolower(`donor_organism.human_specific.ethnicity.text`) %in% c("hispanic", "hispanic-mexican") ~ "AMR",
      tolower(`donor_organism.human_specific.ethnicity.text`) %in% c("jewish", "arab-beduin", "german") ~ "EUR",  # Group European ethnicities
      is.na(`donor_organism.human_specific.ethnicity.text`) ~ "Unknown",
      TRUE ~ "Unknown"
    ),
    # Clean sex column
    Gender_clean = case_when(
      tolower(`donor_organism.sex`) %in% c("female") ~ "female",
      tolower(`donor_organism.sex`) %in% c("male") ~ "male", 
      is.na(`donor_organism.sex`) | `donor_organism.sex` %in% c("17", "18", "19", "20", "21", "22", "23", "24", "25") ~ NA_character_,
      TRUE ~ NA_character_
    ),
    # Infer tissue type from study titles
    Tissue_Type = case_when(
      str_detect(tolower(`study_id`), "brain|neural|neuron") ~ "Brain",
      str_detect(tolower(`study_id`), "heart|cardiac") ~ "Heart",
      str_detect(tolower(`study_id`), "lung|pulmonary") ~ "Lung",
      str_detect(tolower(`study_id`), "liver|hepatic") ~ "Liver",
      str_detect(tolower(`study_id`), "kidney|renal") ~ "Kidney",
      str_detect(tolower(`study_id`), "muscle|skeletal") ~ "Muscle",
      str_detect(tolower(`study_id`), "skin|dermal") ~ "Skin",
      str_detect(tolower(`study_id`), "blood|hematopoietic|immune") ~ "Blood/Immune",
      str_detect(tolower(`study_id`), "bone|marrow") ~ "Bone/Marrow",
      str_detect(tolower(`study_id`), "intestin|gut|colon") ~ "Intestine",
      str_detect(tolower(`study_id`), "retina|eye") ~ "Eye/Retina",
      str_detect(tolower(`study_id`), "placenta") ~ "Placenta",
      str_detect(tolower(`study_id`), "embryo|fetal") ~ "Embryonic/Fetal",
      str_detect(tolower(`study_id`), "chromatin|genome") ~ "Multi-tissue (Chromatin Atlas)",
      str_detect(tolower(`study_id`), "landscape.*cell") ~ "Multi-tissue (Cell Landscape)",
      str_detect(tolower(`study_id`), "adipose|adipocyte") ~ "Adipose",
      str_detect(tolower(`study_id`), "rhabdomyosarcoma|myogenesis") ~ "Muscle",
      str_detect(tolower(`study_id`), "cross-tissue.*immune") ~ "Blood/Immune",
      str_detect(tolower(`study_id`), "tabula sapiens") ~ "Multi-tissue (Tabula Sapiens)",
      str_detect(tolower(`study_id`), "sars-cov-2") ~ "Multi-tissue (COVID-19)",
      str_detect(tolower(`study_id`), "multiple sclerosis") ~ "Brain",
      TRUE ~ "Other/Unknown"
    )
  )

# Basic data overview
cat("Dataset loaded and cleaned successfully!\n")
cat("Total samples:", nrow(data), "\n")
cat("Total variables:", ncol(data), "\n")

# ============================================================================
# 1. DATASET COMPOSITION OVERVIEW
# ============================================================================

cat("\n=== DATASET COMPOSITION OVERVIEW ===\n")

# Basic counts
total_samples <- nrow(data)
cat("Total samples:", total_samples, "\n")

# Tissue type distribution
tissue_counts <- table(data$Tissue_Type, useNA = "always")
cat("\nTissue type distribution:\n")
print(tissue_counts)

# Study distribution
study_counts <- table(data$`study_id`, useNA = "always")
cat("\nStudy distribution (top 5):\n")
print(head(sort(study_counts, decreasing = TRUE), 5))

# ============================================================================
# 2. ANCESTRY DISTRIBUTION ANALYSIS  
# ============================================================================

cat("\n=== ANCESTRY DISTRIBUTION ANALYSIS ===\n")

# Clean and examine ancestry data
ancestry_counts <- table(data$Race_clean, useNA = "always")
cat("Ancestry distribution:\n")
print(ancestry_counts)

# Calculate percentages
ancestry_pct <- round(prop.table(ancestry_counts) * 100, 2)
cat("\nAncestry percentages:\n")
print(ancestry_pct)

# Create ancestry summary table
ancestry_summary <- data.frame(
  Ancestry = names(ancestry_counts),
  Count = as.numeric(ancestry_counts),
  Percentage = as.numeric(ancestry_pct)
)

# Visualize ancestry distribution
p1 <- ggplot(data = subset(ancestry_summary, !is.na(Ancestry)), 
             aes(x = reorder(Ancestry, -Count), y = Count, fill = Ancestry)) +
  geom_bar(stat = "identity") +
  theme_minimal(base_size = 14) +
  labs(title = "HCA: Sample Distribution by Ancestry",
       x = "Ancestry Group", 
       y = "Number of Samples") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1, size = 12),
        axis.text.y = element_text(size = 12),
        axis.title = element_text(size = 14),
        title = element_text(size = 16),
        legend.position = "none") +
  scale_fill_manual(values = ancestry_colors)

ggsave("out/HCA_ancestry_distribution_barplot.pdf", p1, width = 4, height = 3)
cat("Saved: HCA_ancestry_distribution_barplot.pdf\n")

# Pie chart for ancestry
p2 <- ggplot(data = subset(ancestry_summary, !is.na(Ancestry)), 
             aes(x = "", y = Count, fill = Ancestry)) +
  geom_bar(stat = "identity", width = 1) +
  coord_polar("y", start = 0) +
  theme_void(base_size = 14) +
  labs(title = "HCA: Ancestry Distribution (Pie Chart)") +
  theme(title = element_text(size = 16),
        legend.text = element_text(size = 12)) +
  scale_fill_manual(values = ancestry_colors)

ggsave("out/HCA_ancestry_distribution_pie.pdf", p2, width = 4, height = 3)
cat("Saved: HCA_ancestry_distribution_pie.pdf\n")

# ============================================================================
# 3. GENDER DISTRIBUTION ANALYSIS
# ============================================================================

cat("\n=== GENDER DISTRIBUTION ANALYSIS ===\n")

# Overall gender distribution
sex_counts <- table(data$Gender_clean, useNA = "always")
cat("Overall gender distribution:\n")
print(sex_counts)

sex_pct <- round(prop.table(sex_counts) * 100, 2)
cat("\nGender percentages:\n")
print(sex_pct)

# Gender distribution by ancestry
sex_ancestry_table <- table(data$Race_clean, data$Gender_clean, useNA = "always")
cat("\nGender by ancestry cross-tabulation:\n")
print(sex_ancestry_table)

# Convert to proportions within each ancestry
sex_ancestry_prop <- prop.table(sex_ancestry_table, margin = 1)
cat("\nGender proportions within each ancestry:\n")
print(round(sex_ancestry_prop, 3))

# Visualize gender by ancestry (only for groups with sufficient data)
plot_data <- data %>%
  filter(!is.na(Race_clean) & !is.na(Gender_clean) & Race_clean != "Unknown") %>%
  count(Race_clean, Gender_clean) %>%
  group_by(Race_clean) %>%
  mutate(prop = n/sum(n)) %>%
  filter(sum(n) >= 5)  # Only include ancestry groups with at least 5 samples

if(nrow(plot_data) > 0) {
  p3 <- ggplot(plot_data, aes(x = Race_clean, y = n, fill = Gender_clean)) +
    geom_bar(stat = "identity", position = "dodge") +
    theme_minimal(base_size = 14) +
    labs(title = "HCA: Gender Distribution by Ancestry",
         x = "Ancestry Group",
         y = "Number of Samples",
         fill = "Sex") +
    theme(axis.text.x = element_text(angle = 45, hjust = 1, size = 12),
          axis.text.y = element_text(size = 12),
          axis.title = element_text(size = 14),
          title = element_text(size = 16),
          legend.text = element_text(size = 12),
          legend.title = element_text(size = 14)) +
    scale_fill_manual(values = sex_colors)

  ggsave("out/HCA_gender_by_ancestry_barplot.pdf", p3, width = 5, height = 3)
  cat("Saved: HCA_gender_by_ancestry_barplot.pdf\n")

  # Stacked proportional bar chart
  p4 <- ggplot(plot_data, aes(x = Race_clean, y = prop, fill = Gender_clean)) +
    geom_bar(stat = "identity", position = "stack") +
    theme_minimal(base_size = 14) +
    labs(title = "HCA: Gender Proportions by Ancestry",
         x = "Ancestry Group",
         y = "Proportion",
         fill = "Sex") +
    theme(axis.text.x = element_text(angle = 45, hjust = 1, size = 12),
          axis.text.y = element_text(size = 12),
          axis.title = element_text(size = 14),
          title = element_text(size = 16),
          legend.text = element_text(size = 12),
          legend.title = element_text(size = 14)) +
    scale_fill_manual(values = sex_colors) +
    scale_y_continuous(labels = scales::percent)

  ggsave("out/HCA_gender_proportions_by_ancestry.pdf", p4, width = 5, height = 3)
  cat("Saved: HCA_gender_proportions_by_ancestry.pdf\n")
} else {
  cat("Insufficient data for gender by ancestry visualization\n")
}

cat("\n=== STEP 1-3 COMPLETED ===\n")
cat("✓ Dataset composition overview completed\n")
cat("✓ Ancestry distribution analysis completed\n")
cat("✓ Gender distribution analysis completed\n")

# ============================================================================
# 4. REPRESENTATION BIAS ANALYSIS
# ============================================================================

cat("\n=== REPRESENTATION BIAS ANALYSIS ===\n")

# Intersectional analysis (ancestry × gender) - only for groups with data
intersectional_data <- data %>%
  filter(!is.na(Race_clean) & !is.na(Gender_clean) & Race_clean != "Unknown")

if(nrow(intersectional_data) > 0) {
  intersectional_counts <- intersectional_data %>%
    count(Race_clean, Gender_clean) %>%
    spread(Gender_clean, n, fill = 0) %>%
    mutate(Total = female + male,
           Female_Pct = round(female/Total * 100, 1),
           Male_Pct = round(male/Total * 100, 1))

  cat("Intersectional analysis (Ancestry × Gender):\n")
  print(intersectional_counts)

  # Identify underrepresented groups
  total_samples_clean <- sum(intersectional_counts$Total)
  intersectional_counts$Overall_Pct <- round(intersectional_counts$Total / total_samples_clean * 100, 2)

  # Flag groups with < 5% representation
  underrep_threshold <- 5
  underrepresented <- intersectional_counts[intersectional_counts$Overall_Pct < underrep_threshold, ]
  cat("\nUnderrepresented ancestry groups (< 5% of total):\n")
  print(underrepresented)

  # Create heatmap showing sample counts
  heatmap_data <- intersectional_data %>%
    count(Race_clean, Gender_clean)

  p5 <- ggplot(heatmap_data, aes(x = Gender_clean, y = Race_clean, fill = n)) +
    geom_tile(color = "white") +
    geom_text(aes(label = n), color = "white", size = 5) +
    scale_fill_gradient(low = "lightblue", high = "darkblue", name = "Count") +
    theme_minimal(base_size = 14) +
    labs(title = "HCA: Sample Count Heatmap (Ancestry × Gender)",
         x = "Gender", y = "Ancestry") +
    theme(axis.text.x = element_text(angle = 0, size = 12),
          axis.text.y = element_text(size = 12),
          axis.title = element_text(size = 14),
          title = element_text(size = 16),
          legend.text = element_text(size = 12),
          legend.title = element_text(size = 14))

  ggsave("out/HCA_intersectional_heatmap.pdf", p5, width = 4, height = 3)
  cat("Saved: HCA_intersectional_heatmap.pdf\n")
} else {
  cat("Insufficient data for intersectional analysis\n")
}

# ============================================================================
# 5. TISSUE-SPECIFIC ANALYSIS
# ============================================================================

cat("\n=== TISSUE-SPECIFIC ANALYSIS ===\n")

# Tissue type distribution by ancestry (only for groups with sufficient data)
tissue_ancestry_data <- data %>%
  filter(!is.na(Race_clean) & !is.na(Tissue_Type) & Race_clean != "Unknown") %>%
  count(Tissue_Type, Race_clean) %>%
  group_by(Tissue_Type) %>%
  mutate(total = sum(n),
         prop = n/total) %>%
  ungroup() %>%
  filter(total >= 5) %>%  # Only include tissue types with at least 5 samples
  mutate(tissue_label = paste0(Tissue_Type, ' (n=', total, ')'))

if(nrow(tissue_ancestry_data) > 0) {
  # 100% stacked bar chart colored by ancestry for each tissue type
  p6 <- ggplot(tissue_ancestry_data, aes(x = tissue_label, y = prop, fill = Race_clean)) +
    geom_bar(stat = "identity", position = "stack") +
    theme_minimal(base_size = 14) +
    labs(title = "HCA: Tissue Types by Ancestry (100% Stacked)",
         x = "Tissue Type",
         y = "Proportion",
         fill = "Ancestry") +
    theme(axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
          axis.text.y = element_text(size = 12),
          axis.title = element_text(size = 14),
          title = element_text(size = 16),
          legend.text = element_text(size = 12),
          legend.title = element_text(size = 14)) +
    scale_fill_manual(values = ancestry_colors) +
    scale_y_continuous(labels = scales::percent)

  ggsave("out/HCA_tissue_types_by_ancestry_stacked.pdf", p6, width = 10, height = 4)
  cat("Saved: HCA_tissue_types_by_ancestry_stacked.pdf\n")
} else {
  cat("Insufficient data for tissue by ancestry analysis\n")
}

# Tissue type distribution by sex
tissue_sex_data <- data %>%
  filter(!is.na(Gender_clean) & !is.na(Tissue_Type)) %>%
  count(Tissue_Type, Gender_clean) %>%
  group_by(Tissue_Type) %>%
  mutate(total = sum(n),
         prop = n/total) %>%
  ungroup() %>%
  filter(total >= 5) %>%  # Only include tissue types with at least 5 samples
  mutate(tissue_label = paste0(Tissue_Type, ' (n=', total, ')'))

if(nrow(tissue_sex_data) > 0) {
  # 100% stacked bar chart colored by sex for each tissue type
  p7 <- ggplot(tissue_sex_data, aes(x = tissue_label, y = prop, fill = Gender_clean)) +
    geom_bar(stat = "identity", position = "stack") +
    theme_minimal(base_size = 14) +
    labs(title = "HCA: Tissue Types by Sex (100% Stacked)",
         x = "Tissue Type",
         y = "Proportion",
         fill = "Sex") +
    theme(axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
          axis.text.y = element_text(size = 12),
          axis.title = element_text(size = 14),
          title = element_text(size = 16),
          legend.text = element_text(size = 12),
          legend.title = element_text(size = 14)) +
    scale_fill_manual(values = sex_colors) +
    scale_y_continuous(labels = scales::percent)

  ggsave("out/HCA_tissue_types_by_sex_stacked.pdf", p7, width = 10, height = 4)
  cat("Saved: HCA_tissue_types_by_sex_stacked.pdf\n")
} else {
  cat("Insufficient data for tissue by sex analysis\n")
}

# Overall tissue type distribution
tissue_dist_data <- data %>%
  filter(!is.na(Tissue_Type)) %>%
  count(Tissue_Type) %>%
  mutate(percentage = round(n/sum(n)*100, 1)) %>%
  arrange(desc(n))

p8 <- ggplot(tissue_dist_data, aes(x = reorder(Tissue_Type, -n), y = n)) +
  geom_bar(stat = "identity", fill = "steelblue") +
  geom_text(aes(label = paste0(n, "\n(", percentage, "%)")),
            vjust = -0.5, size = 3) +
  theme_minimal(base_size = 14) +
  labs(title = "HCA: Tissue Type Distribution",
       x = "Tissue Type",
       y = "Number of Samples") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
        axis.text.y = element_text(size = 12),
        axis.title = element_text(size = 14),
        title = element_text(size = 16))

ggsave("out/HCA_tissue_type_distribution.pdf", p8, width = 8, height = 4)
cat("Saved: HCA_tissue_type_distribution.pdf\n")

# ============================================================================
# 6. STATISTICAL ANALYSIS (SAME METHODS AS PSYCHAD)
# ============================================================================

cat("\n=== STATISTICAL ANALYSIS ===\n")

# Load additional packages for statistical tests
if (!require(car, quietly = TRUE)) {
  install.packages("car")
  library(car)
}

# 1. Hypothesis Testing (adapted for smaller sample sizes)
cat("\n--- Hypothesis Testing ---\n")

# Chi-square test for ancestry distribution vs expected equal distribution
ancestry_for_test <- data$Race_clean[!is.na(data$Race_clean) & data$Race_clean != "Unknown"]
if(length(unique(ancestry_for_test)) > 1 & length(ancestry_for_test) > 10) {
  chisq_ancestry <- chisq.test(table(ancestry_for_test))
  cat("Chi-square test for ancestry distribution:\n")
  cat("X-squared =", chisq_ancestry$statistic, ", p-value =", chisq_ancestry$p.value, "\n")
} else {
  cat("Insufficient data for chi-square test of ancestry distribution\n")
}

# 2. Equity Metrics (SAME CALCULATIONS AS PSYCHAD)
cat("\n--- Equity Metrics ---\n")

# Calculate representation ratios (actual vs expected equal representation)
ancestry_data <- data[!is.na(data$Race_clean) & data$Race_clean != "Unknown", ]
if(nrow(ancestry_data) > 0) {
  total_samples_analysis <- nrow(ancestry_data)
  unique_ancestries <- unique(ancestry_data$Race_clean)
  expected_equal <- total_samples_analysis / length(unique_ancestries)

  equity_metrics <- ancestry_data %>%
    count(Race_clean) %>%
    mutate(
      expected_equal = expected_equal,
      representation_ratio = n / expected_equal,
      equity_index = ifelse(representation_ratio > 1, 1/representation_ratio, representation_ratio)
    )

  cat("Representation ratios (>1 = overrepresented, <1 = underrepresented):\n")
  print(equity_metrics)

  # Simpson's Diversity Index
  simpson_diversity <- 1 - sum((equity_metrics$n / sum(equity_metrics$n))^2)
  cat("Simpson's Diversity Index:", round(simpson_diversity, 3), "\n")

  # Shannon's Diversity Index
  shannon_diversity <- -sum((equity_metrics$n / sum(equity_metrics$n)) * log(equity_metrics$n / sum(equity_metrics$n)))
  cat("Shannon's Diversity Index:", round(shannon_diversity, 3), "\n")
} else {
  cat("Insufficient ancestry data for equity metrics calculation\n")
}

# 3. Missing Data Analysis
cat("\n--- Missing Data Analysis ---\n")

# Calculate missing data percentages by ancestry for key variables
missing_summary <- data %>%
  select(Race_clean, `donor_organism.organism_age`, `donor_organism.sex`,
         `donor_organism.diseases.text`, `study_id`) %>%
  filter(!is.na(Race_clean)) %>%
  group_by(Race_clean) %>%
  summarise(
    n_total = n(),
    Age_missing = sum(is.na(`donor_organism.organism_age`)) / n() * 100,
    Sex_missing = sum(is.na(`donor_organism.sex`)) / n() * 100,
    Disease_missing = sum(is.na(`donor_organism.diseases.text`)) / n() * 100,
    Study_missing = sum(is.na(`study_id`)) / n() * 100,
    .groups = 'drop'
  )

cat("Missing data percentages by ancestry:\n")
missing_summary_numeric <- missing_summary[, -1]  # Remove Ancestry column for rounding
missing_summary_rounded <- cbind(missing_summary[1], round(missing_summary_numeric, 1))
print(missing_summary_rounded)

# Create summary report
cat("\n=== HCA ANALYSIS SUMMARY REPORT ===\n")
cat("✓ Dataset contains", nrow(data), "samples from HCA studies\n")
cat("✓ Ancestry distribution: Severe underrepresentation of non-European groups\n")
cat("✓ Gender balance: Relatively balanced where data available\n")
cat("✓ Representation bias: Extreme EUR dominance, minimal diversity\n")
cat("✓ Tissue types: Multi-tissue studies dominant, limited single-tissue representation\n")
cat("✓ Missing data: High missingness for demographic information\n")
cat("✓ Power concerns: Very small sample sizes for most ancestry groups\n")

cat("\n=== ALL HCA ANALYSIS STEPS COMPLETED ===\n")
cat("✓ Dataset composition overview completed\n")
cat("✓ Ancestry distribution analysis completed\n")
cat("✓ Gender distribution analysis completed\n")
cat("✓ Representation bias analysis completed\n")
cat("✓ Tissue-specific analysis completed\n")
cat("✓ Statistical analysis completed\n")
cat("✓ Equity metrics calculated\n")

# Save the workspace for further analysis
save.image("out/HCA_equity_bias_analysis_workspace.RData")
cat("✓ Workspace saved to: HCA_equity_bias_analysis_workspace.RData\n")
