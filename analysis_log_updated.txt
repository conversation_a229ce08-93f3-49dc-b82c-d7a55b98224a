mkdir: out: File exists

Attaching package: ‘dplyr’

The following objects are masked from ‘package:plyr’:

    arrange, count, desc, failwith, id, mutate, rename, summarise,
    summarize

The following objects are masked from ‘package:stats’:

    filter, lag

The following objects are masked from ‘package:base’:

    intersect, setdiff, setequal, union


Attaching package: ‘tidyr’

The following object is masked from ‘package:reshape2’:

    smiths

Loading psychAD media dataset...
Dataset loaded successfully!
Total samples: 1494 
Total variables: 28 

=== DATASET COMPOSITION OVERVIEW ===
Total samples: 1494 
Cohort distribution:

HBCC MSBB RADC 
 300 1042  152 

=== ANCESTRY DISTRIBUTION ANALYSIS ===
Ancestry distribution:

    AFR     AMR     EAS     EUR     SAS Unknown    <NA> 
    338     135      14     985       5      17       0 

Ancestry percentages:

    AFR     AMR     EAS     EUR     SAS Unknown    <NA> 
  22.62    9.04    0.94   65.93    0.33    1.14    0.00 
Saved: ancestry_distribution_barplot.pdf
Saved: ancestry_distribution_pie.pdf

=== GENDER DISTRIBUTION ANALYSIS ===
Overall gender distribution:

female   male   <NA> 
   771    723      0 

Gender percentages:

female   male   <NA> 
 51.61  48.39   0.00 

Gender by ancestry cross-tabulation:
         
          female male <NA>
  AFR        181  157    0
  AMR         65   70    0
  EAS          6    8    0
  EUR        510  475    0
  SAS          0    5    0
  Unknown      9    8    0
  <NA>         0    0    0

Gender proportions within each ancestry:
         
          female  male  <NA>
  AFR      0.536 0.464 0.000
  AMR      0.481 0.519 0.000
  EAS      0.429 0.571 0.000
  EUR      0.518 0.482 0.000
  SAS      0.000 1.000 0.000
  Unknown  0.529 0.471 0.000
  <NA>                      
Saved: gender_by_ancestry_barplot.pdf
Saved: gender_proportions_by_ancestry.pdf

=== STEP 1-3 COMPLETED ===
✓ Dataset composition overview completed
✓ Ancestry distribution analysis completed
✓ Gender distribution analysis completed

=== REPRESENTATION BIAS ANALYSIS ===
Intersectional analysis (Ancestry × Gender):
  Ancestry_clean female male Total Female_Pct Male_Pct
1            AFR    181  157   338       53.6     46.4
2            AMR     65   70   135       48.1     51.9
3            EAS      6    8    14       42.9     57.1
4            EUR    510  475   985       51.8     48.2
5            SAS      0    5     5        0.0    100.0
6        Unknown      9    8    17       52.9     47.1

Underrepresented ancestry groups (< 5% of total):
  Ancestry_clean female male Total Female_Pct Male_Pct Overall_Pct
3            EAS      6    8    14       42.9     57.1        0.94
5            SAS      0    5     5        0.0    100.0        0.33
6        Unknown      9    8    17       52.9     47.1        1.14
Saved: intersectional_heatmap.pdf

Missing data patterns by ancestry and gender:
# A tibble: 55 × 6
   Ancestry_clean sex    variable     missing_count total_count missing_pct
   <chr>          <chr>  <chr>                <int>       <int>       <dbl>
 1 AFR            female Braak                   89         181        49.2
 2 AFR            female CERAD                   89         181        49.2
 3 AFR            female PMI                      0         181         0  
 4 AFR            female ageDeath                 0         181         0  
 5 AFR            female apoeGenotype             5         181         2.8
 6 AFR            male   Braak                  111         157        70.7
 7 AFR            male   CERAD                  111         157        70.7
 8 AFR            male   PMI                      0         157         0  
 9 AFR            male   ageDeath                 0         157         0  
10 AFR            male   apoeGenotype             4         157         2.5
# ℹ 45 more rows

=== STEP 4 COMPLETED ===
✓ Representation bias analysis completed

=== STATISTICAL ANALYSIS ===

Attaching package: ‘car’

The following object is masked from ‘package:dplyr’:

    recode


--- Hypothesis Testing ---
Chi-square test for ancestry distribution:
X-squared = 2256.632 , p-value = 0 
Gender balance in AFR - Chi-square p-value: 0.1917472 
Gender balance in AMR - Chi-square p-value: 0.6669545 
Gender balance in EUR - Chi-square p-value: 0.2647678 
Gender balance in EAS - Chi-square p-value: 0.5929801 

--- Equity Metrics ---
Representation ratios (>1 = overrepresented, <1 = underrepresented):
  Ancestry_clean   n expected_equal representation_ratio equity_index
1            AFR 338          295.4           1.14421124   0.87396450
2            AMR 135          295.4           0.45700745   0.45700745
3            EAS  14          295.4           0.04739336   0.04739336
4            EUR 985          295.4           3.33446175   0.29989848
5            SAS   5          295.4           0.01692620   0.01692620
Simpson's Diversity Index: 0.494 
Shannon's Diversity Index: 0.89 

--- Missing Data Analysis ---
Missing data percentages by ancestry:
  Ancestry_clean n_total PMI_missing apoeGenotype_missing CERAD_missing
1            AFR     338           0                  2.7          59.2
2            AMR     135           0                  0.7          46.7
3            EAS      14           0                  0.0          64.3
4            EUR     985           0                  1.7          53.2
5            SAS       5           0                  0.0          80.0
6        Unknown      17           0                 58.8          58.8
  Braak_missing Dementia_missing
1          59.2             60.1
2          44.4             48.1
3          64.3             64.3
4          52.5             54.6
5          80.0             80.0
6          58.8             70.6

=== ANALYSIS SUMMARY REPORT ===
✓ Dataset contains 1494 samples across 3 cohorts
✓ Ancestry distribution: EUR dominant, AFR substantial, others underrepresented
✓ Gender balance: Overall balanced, but varies by ancestry
✓ Representation bias: EAS, SAS, Unknown groups severely underrepresented
✓ Missing data: Substantial gaps in clinical measures (CERAD, Braak) for some ancestry groups
✓ Power concerns: Small ancestry groups may be underpowered for analyses

=== ALL ANALYSIS STEPS COMPLETED ===
✓ Dataset composition overview completed
✓ Ancestry distribution analysis completed
✓ Gender distribution analysis completed
✓ Representation bias analysis completed
✓ Statistical analysis completed
✓ Equity metrics calculated
✓ Additional plots completed
✓ Workspace saved to: equity_bias_analysis_workspace.RData

=== ADDITIONAL VISUALIZATION ANALYSIS ===
Saved: ancestry_distribution_whole_cohort.pdf
Saved: disease_diagnoses_by_ancestry_stacked.pdf
Saved: disease_diagnoses_by_sex_stacked.pdf

=== ADDITIONAL PLOTS COMPLETED ===
✓ Ancestry distribution whole cohort completed
✓ Disease diagnoses by ancestry (stacked) completed
✓ Disease diagnoses by sex (stacked) completed
